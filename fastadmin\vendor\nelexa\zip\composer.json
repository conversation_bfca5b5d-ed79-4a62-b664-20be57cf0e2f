{"name": "nelexa/zip", "type": "library", "description": "PhpZip is a php-library for extended work with ZIP-archives. Open, create, update, delete, extract and get info tool. Supports appending to existing ZIP files, WinZip AES encryption, Traditional PKWARE Encryption, BZIP2 compression, external file attributes and ZIP64 extensions. Alternative ZipArchive. It does not require php-zip extension.", "keywords": ["zip", "unzip", "archive", "extract", "winzip", "ziparchive"], "homepage": "https://github.com/Ne-Lexa/php-zip", "license": "MIT", "authors": [{"name": "Ne-Lexa", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^7.4 || ^8.0", "ext-zlib": "*", "psr/http-message": "*", "symfony/finder": "*"}, "require-dev": {"ext-iconv": "*", "ext-bz2": "*", "ext-openssl": "*", "ext-fileinfo": "*", "ext-xml": "*", "ext-dom": "*", "guzzlehttp/psr7": "^1.6", "phpunit/phpunit": "^9", "symfony/var-dumper": "*", "friendsofphp/php-cs-fixer": "^3.4.0", "vimeo/psalm": "^4.6", "symfony/http-foundation": "*"}, "autoload": {"psr-4": {"PhpZip\\": "src/"}}, "autoload-dev": {"psr-4": {"PhpZip\\Tests\\": "tests/"}}, "suggest": {"ext-iconv": "Needed to support convert zip entry name to requested character encoding", "ext-openssl": "Needed to support encrypt zip entries or use ext-mcrypt", "ext-bz2": "Needed to support BZIP2 compression", "ext-fileinfo": "Needed to get mime-type file"}, "minimum-stability": "stable", "scripts": {"php:fix": "php .php_cs --force", "test": "phpunit --configuration phpunit.xml --do-not-cache-result --colors=always", "test:coverage": "phpunit --configuration phpunit.xml --do-not-cache-result --colors=always --coverage-clover build/logs/clover.xml --coverage-html build/coverage"}}