{"name": "maennchen/zipstream-php", "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["zip", "stream"], "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.4 || ^8.0", "symfony/polyfill-mbstring": "^1.0", "psr/http-message": "^1.0", "myclabs/php-enum": "^1.5"}, "require-dev": {"phpunit/phpunit": "^8.5.8 || ^9.4.2", "guzzlehttp/guzzle": "^6.5.3 || ^7.2.0", "ext-zip": "*", "mikey179/vfsstream": "^1.6", "vimeo/psalm": "^4.1", "php-coveralls/php-coveralls": "^2.4", "friendsofphp/php-cs-fixer": "^3.9"}, "scripts": {"format": "php-cs-fixer fix", "test": "composer run test:unit && composer run test:formatted && composer run test:lint", "test:unit": "phpunit --coverage-clover=coverage.clover.xml", "test:formatted": "composer run format -- --dry-run --stop-on-violation --using-cache=no", "test:lint": "psalm --stats --show-info --find-unused-psalm-suppress", "coverage:report": "php-coveralls --coverage_clover=coverage.clover.xml --json_path=coveralls-upload.json -v", "install:tools": "phive install --trust-gpg-keys 0x67F861C3D889C656", "docs:generate": "tools/phpdocumentor --sourcecode"}, "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "archive": {"exclude": ["/composer.lock", "/docs", "/.gitattributes", "/.github", "/.giti<PERSON>re", "/guides", "/.phive", "/.php-cs-fixer.cache", "/.php-cs-fixer.dist.php", "/.phpdoc", "/phpdoc.dist.xml", "/.phpunit.result.cache", "/phpunit.xml.dist", "/psalm.xml", "/test", "/tools", "/.tool-versions", "/vendor"]}}