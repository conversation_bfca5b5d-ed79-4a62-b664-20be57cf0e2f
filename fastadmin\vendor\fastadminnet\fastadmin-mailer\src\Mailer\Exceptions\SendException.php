<?php
/***************************************************\
 *
 *  <PERSON><PERSON> (https://github.com/txthinking/Mailer)
 *
 *  A lightweight PHP SMTP mail sender.
 *  Implement RFC0821, RFC0822, RFC1869, RFC2045, RFC2821
 *
 *  Support html body, don't worry that the receiver's
 *  mail client can't support html, because <PERSON><PERSON> will
 *  send both text/plain and text/html body, so if the
 *  mail client can't support html, it will display the
 *  text/plain body.
 *
 *  Create Date 2012-07-25.
 *  Under the MIT license.
 *
 \***************************************************/
/**
 * Created by PhpStorm.
 * User: msowers
 * Date: 3/30/15
 * Time: 1:47 PM
 */

namespace Tx\Mailer\Exceptions;

class SendException extends SMTPException
{

}
