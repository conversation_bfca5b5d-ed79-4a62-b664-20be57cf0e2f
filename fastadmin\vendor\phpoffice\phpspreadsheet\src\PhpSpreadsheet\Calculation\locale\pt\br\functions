############################################################
##
## PhpSpreadsheet - function name translations
##
## Portug<PERSON><PERSON><PERSON> (Brazilian Portuguese)
##
############################################################


##
## Funções de cubo (Cube Functions)
##
CUBEKPIMEMBER = MEMBROKPICUBO
CUBEMEMBER = MEMBROCUBO
CUBEMEMBERPROPERTY = PROPRIEDADEMEMBROCUBO
CUBERANKEDMEMBER = MEMBROCLASSIFICADOCUBO
CUBESET = CONJUNTOCUBO
CUBESETCOUNT = CONTAGEMCONJUNTOCUBO
CUBEVALUE = VALORCUBO

##
## Funções de banco de dados (Database Functions)
##
DAVERAGE = BDMÉDIA
DCOUNT = BDCONTAR
DCOUNTA = BDCONTARA
DGET = BDEXTRAIR
DMAX = BDMÁX
DMIN = BDMÍN
DPRODUCT = BDMULTIPL
DSTDEV = BDEST
DSTDEVP = BDDESVPA
DSUM = BDSOMA
DVAR = BDVAREST
DVARP = BDVARP

##
## Funções de data e hora (Date & Time Functions)
##
DATE = DATA
DATEDIF = DATADIF
DATESTRING = DATA.SÉRIE
DATEVALUE = DATA.VALOR
DAY = DIA
DAYS = DIAS
DAYS360 = DIAS360
EDATE = DATAM
EOMONTH = FIMMÊS
HOUR = HORA
ISOWEEKNUM = NÚMSEMANAISO
MINUTE = MINUTO
MONTH = MÊS
NETWORKDAYS = DIATRABALHOTOTAL
NETWORKDAYS.INTL = DIATRABALHOTOTAL.INTL
NOW = AGORA
SECOND = SEGUNDO
TIME = TEMPO
TIMEVALUE = VALOR.TEMPO
TODAY = HOJE
WEEKDAY = DIA.DA.SEMANA
WEEKNUM = NÚMSEMANA
WORKDAY = DIATRABALHO
WORKDAY.INTL = DIATRABALHO.INTL
YEAR = ANO
YEARFRAC = FRAÇÃOANO

##
## Funções de engenharia (Engineering Functions)
##
BESSELI = BESSELI
BESSELJ = BESSELJ
BESSELK = BESSELK
BESSELY = BESSELY
BIN2DEC = BINADEC
BIN2HEX = BINAHEX
BIN2OCT = BINAOCT
BITAND = BITAND
BITLSHIFT = DESLOCESQBIT
BITOR = BITOR
BITRSHIFT = DESLOCDIRBIT
BITXOR = BITXOR
COMPLEX = COMPLEXO
CONVERT = CONVERTER
DEC2BIN = DECABIN
DEC2HEX = DECAHEX
DEC2OCT = DECAOCT
DELTA = DELTA
ERF = FUNERRO
ERF.PRECISE = FUNERRO.PRECISO
ERFC = FUNERROCOMPL
ERFC.PRECISE = FUNERROCOMPL.PRECISO
GESTEP = DEGRAU
HEX2BIN = HEXABIN
HEX2DEC = HEXADEC
HEX2OCT = HEXAOCT
IMABS = IMABS
IMAGINARY = IMAGINÁRIO
IMARGUMENT = IMARG
IMCONJUGATE = IMCONJ
IMCOS = IMCOS
IMCOSH = IMCOSH
IMCOT = IMCOT
IMCSC = IMCOSEC
IMCSCH = IMCOSECH
IMDIV = IMDIV
IMEXP = IMEXP
IMLN = IMLN
IMLOG10 = IMLOG10
IMLOG2 = IMLOG2
IMPOWER = IMPOT
IMPRODUCT = IMPROD
IMREAL = IMREAL
IMSEC = IMSEC
IMSECH = IMSECH
IMSIN = IMSENO
IMSINH = IMSENH
IMSQRT = IMRAIZ
IMSUB = IMSUBTR
IMSUM = IMSOMA
IMTAN = IMTAN
OCT2BIN = OCTABIN
OCT2DEC = OCTADEC
OCT2HEX = OCTAHEX

##
## Funções financeiras (Financial Functions)
##
ACCRINT = JUROSACUM
ACCRINTM = JUROSACUMV
AMORDEGRC = AMORDEGRC
AMORLINC = AMORLINC
COUPDAYBS = CUPDIASINLIQ
COUPDAYS = CUPDIAS
COUPDAYSNC = CUPDIASPRÓX
COUPNCD = CUPDATAPRÓX
COUPNUM = CUPNÚM
COUPPCD = CUPDATAANT
CUMIPMT = PGTOJURACUM
CUMPRINC = PGTOCAPACUM
DB = BD
DDB = BDD
DISC = DESC
DOLLARDE = MOEDADEC
DOLLARFR = MOEDAFRA
DURATION = DURAÇÃO
EFFECT = EFETIVA
FV = VF
FVSCHEDULE = VFPLANO
INTRATE = TAXAJUROS
IPMT = IPGTO
IRR = TIR
ISPMT = ÉPGTO
MDURATION = MDURAÇÃO
MIRR = MTIR
NOMINAL = NOMINAL
NPER = NPER
NPV = VPL
ODDFPRICE = PREÇOPRIMINC
ODDFYIELD = LUCROPRIMINC
ODDLPRICE = PREÇOÚLTINC
ODDLYIELD = LUCROÚLTINC
PDURATION = DURAÇÃOP
PMT = PGTO
PPMT = PPGTO
PRICE = PREÇO
PRICEDISC = PREÇODESC
PRICEMAT = PREÇOVENC
PV = VP
RATE = TAXA
RECEIVED = RECEBER
RRI = TAXAJURO
SLN = DPD
SYD = SDA
TBILLEQ = OTN
TBILLPRICE = OTNVALOR
TBILLYIELD = OTNLUCRO
VDB = BDV
XIRR = XTIR
XNPV = XVPL
YIELD = LUCRO
YIELDDISC = LUCRODESC
YIELDMAT = LUCROVENC

##
## Funções de informação (Information Functions)
##
CELL = CÉL
ERROR.TYPE = TIPO.ERRO
INFO = INFORMAÇÃO
ISBLANK = ÉCÉL.VAZIA
ISERR = ÉERRO
ISERROR = ÉERROS
ISEVEN = ÉPAR
ISFORMULA = ÉFÓRMULA
ISLOGICAL = ÉLÓGICO
ISNA = É.NÃO.DISP
ISNONTEXT = É.NÃO.TEXTO
ISNUMBER = ÉNÚM
ISODD = ÉIMPAR
ISREF = ÉREF
ISTEXT = ÉTEXTO
N = N
NA = NÃO.DISP
SHEET = PLAN
SHEETS = PLANS
TYPE = TIPO

##
## Funções lógicas (Logical Functions)
##
AND = E
FALSE = FALSO
IF = SE
IFERROR = SEERRO
IFNA = SENÃODISP
IFS = SES
NOT = NÃO
OR = OU
SWITCH = PARÂMETRO
TRUE = VERDADEIRO
XOR = XOR

##
## Funções de pesquisa e referência (Lookup & Reference Functions)
##
ADDRESS = ENDEREÇO
AREAS = ÁREAS
CHOOSE = ESCOLHER
COLUMN = COL
COLUMNS = COLS
FORMULATEXT = FÓRMULATEXTO
GETPIVOTDATA = INFODADOSTABELADINÂMICA
HLOOKUP = PROCH
HYPERLINK = HIPERLINK
INDEX = ÍNDICE
INDIRECT = INDIRETO
LOOKUP = PROC
MATCH = CORRESP
OFFSET = DESLOC
ROW = LIN
ROWS = LINS
RTD = RTD
TRANSPOSE = TRANSPOR
VLOOKUP = PROCV
*RC = LC

##
## Funções matemáticas e trigonométricas (Math & Trig Functions)
##
ABS = ABS
ACOS = ACOS
ACOSH = ACOSH
ACOT = ACOT
ACOTH = ACOTH
AGGREGATE = AGREGAR
ARABIC = ARÁBICO
ASIN = ASEN
ASINH = ASENH
ATAN = ATAN
ATAN2 = ATAN2
ATANH = ATANH
BASE = BASE
CEILING.MATH = TETO.MAT
CEILING.PRECISE = TETO.PRECISO
COMBIN = COMBIN
COMBINA = COMBINA
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = COSEC
CSCH = COSECH
DECIMAL = DECIMAL
DEGREES = GRAUS
ECMA.CEILING = ECMA.TETO
EVEN = PAR
EXP = EXP
FACT = FATORIAL
FACTDOUBLE = FATDUPLO
FLOOR.MATH = ARREDMULTB.MAT
FLOOR.PRECISE = ARREDMULTB.PRECISO
GCD = MDC
INT = INT
ISO.CEILING = ISO.TETO
LCM = MMC
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = MATRIZ.DETERM
MINVERSE = MATRIZ.INVERSO
MMULT = MATRIZ.MULT
MOD = MOD
MROUND = MARRED
MULTINOMIAL = MULTINOMIAL
MUNIT = MUNIT
ODD = ÍMPAR
PI = PI
POWER = POTÊNCIA
PRODUCT = MULT
QUOTIENT = QUOCIENTE
RADIANS = RADIANOS
RAND = ALEATÓRIO
RANDBETWEEN = ALEATÓRIOENTRE
ROMAN = ROMANO
ROUND = ARRED
ROUNDDOWN = ARREDONDAR.PARA.BAIXO
ROUNDUP = ARREDONDAR.PARA.CIMA
SEC = SEC
SECH = SECH
SERIESSUM = SOMASEQÜÊNCIA
SIGN = SINAL
SIN = SEN
SINH = SENH
SQRT = RAIZ
SQRTPI = RAIZPI
SUBTOTAL = SUBTOTAL
SUM = SOMA
SUMIF = SOMASE
SUMIFS = SOMASES
SUMPRODUCT = SOMARPRODUTO
SUMSQ = SOMAQUAD
SUMX2MY2 = SOMAX2DY2
SUMX2PY2 = SOMAX2SY2
SUMXMY2 = SOMAXMY2
TAN = TAN
TANH = TANH
TRUNC = TRUNCAR

##
## Funções estatísticas (Statistical Functions)
##
AVEDEV = DESV.MÉDIO
AVERAGE = MÉDIA
AVERAGEA = MÉDIAA
AVERAGEIF = MÉDIASE
AVERAGEIFS = MÉDIASES
BETA.DIST = DIST.BETA
BETA.INV = INV.BETA
BINOM.DIST = DISTR.BINOM
BINOM.DIST.RANGE = INTERV.DISTR.BINOM
BINOM.INV = INV.BINOM
CHISQ.DIST = DIST.QUIQUA
CHISQ.DIST.RT = DIST.QUIQUA.CD
CHISQ.INV = INV.QUIQUA
CHISQ.INV.RT = INV.QUIQUA.CD
CHISQ.TEST = TESTE.QUIQUA
CONFIDENCE.NORM = INT.CONFIANÇA.NORM
CONFIDENCE.T = INT.CONFIANÇA.T
CORREL = CORREL
COUNT = CONT.NÚM
COUNTA = CONT.VALORES
COUNTBLANK = CONTAR.VAZIO
COUNTIF = CONT.SE
COUNTIFS = CONT.SES
COVARIANCE.P = COVARIAÇÃO.P
COVARIANCE.S = COVARIAÇÃO.S
DEVSQ = DESVQ
EXPON.DIST = DISTR.EXPON
F.DIST = DIST.F
F.DIST.RT = DIST.F.CD
F.INV = INV.F
F.INV.RT = INV.F.CD
F.TEST = TESTE.F
FISHER = FISHER
FISHERINV = FISHERINV
FORECAST.ETS = PREVISÃO.ETS
FORECAST.ETS.CONFINT = PREVISÃO.ETS.CONFINT
FORECAST.ETS.SEASONALITY = PREVISÃO.ETS.SAZONALIDADE
FORECAST.ETS.STAT = PREVISÃO.ETS.STAT
FORECAST.LINEAR = PREVISÃO.LINEAR
FREQUENCY = FREQÜÊNCIA
GAMMA = GAMA
GAMMA.DIST = DIST.GAMA
GAMMA.INV = INV.GAMA
GAMMALN = LNGAMA
GAMMALN.PRECISE = LNGAMA.PRECISO
GAUSS = GAUSS
GEOMEAN = MÉDIA.GEOMÉTRICA
GROWTH = CRESCIMENTO
HARMEAN = MÉDIA.HARMÔNICA
HYPGEOM.DIST = DIST.HIPERGEOM.N
INTERCEPT = INTERCEPÇÃO
KURT = CURT
LARGE = MAIOR
LINEST = PROJ.LIN
LOGEST = PROJ.LOG
LOGNORM.DIST = DIST.LOGNORMAL.N
LOGNORM.INV = INV.LOGNORMAL
MAX = MÁXIMO
MAXA = MÁXIMOA
MAXIFS = MÁXIMOSES
MEDIAN = MED
MIN = MÍNIMO
MINA = MÍNIMOA
MINIFS = MÍNIMOSES
MODE.MULT = MODO.MULT
MODE.SNGL = MODO.ÚNICO
NEGBINOM.DIST = DIST.BIN.NEG.N
NORM.DIST = DIST.NORM.N
NORM.INV = INV.NORM.N
NORM.S.DIST = DIST.NORMP.N
NORM.S.INV = INV.NORMP.N
PEARSON = PEARSON
PERCENTILE.EXC = PERCENTIL.EXC
PERCENTILE.INC = PERCENTIL.INC
PERCENTRANK.EXC = ORDEM.PORCENTUAL.EXC
PERCENTRANK.INC = ORDEM.PORCENTUAL.INC
PERMUT = PERMUT
PERMUTATIONA = PERMUTAS
PHI = PHI
POISSON.DIST = DIST.POISSON
PROB = PROB
QUARTILE.EXC = QUARTIL.EXC
QUARTILE.INC = QUARTIL.INC
RANK.AVG = ORDEM.MÉD
RANK.EQ = ORDEM.EQ
RSQ = RQUAD
SKEW = DISTORÇÃO
SKEW.P = DISTORÇÃO.P
SLOPE = INCLINAÇÃO
SMALL = MENOR
STANDARDIZE = PADRONIZAR
STDEV.P = DESVPAD.P
STDEV.S = DESVPAD.A
STDEVA = DESVPADA
STDEVPA = DESVPADPA
STEYX = EPADYX
T.DIST = DIST.T
T.DIST.2T = DIST.T.BC
T.DIST.RT = DIST.T.CD
T.INV = INV.T
T.INV.2T = INV.T.BC
T.TEST = TESTE.T
TREND = TENDÊNCIA
TRIMMEAN = MÉDIA.INTERNA
VAR.P = VAR.P
VAR.S = VAR.A
VARA = VARA
VARPA = VARPA
WEIBULL.DIST = DIST.WEIBULL
Z.TEST = TESTE.Z

##
## Funções de texto (Text Functions)
##
BAHTTEXT = BAHTTEXT
CHAR = CARACT
CLEAN = TIRAR
CODE = CÓDIGO
CONCAT = CONCAT
DOLLAR = MOEDA
EXACT = EXATO
FIND = PROCURAR
FIXED = DEF.NÚM.DEC
LEFT = ESQUERDA
LEN = NÚM.CARACT
LOWER = MINÚSCULA
MID = EXT.TEXTO
NUMBERSTRING = SEQÜÊNCIA.NÚMERO
NUMBERVALUE = VALORNUMÉRICO
PHONETIC = FONÉTICA
PROPER = PRI.MAIÚSCULA
REPLACE = MUDAR
REPT = REPT
RIGHT = DIREITA
SEARCH = LOCALIZAR
SUBSTITUTE = SUBSTITUIR
T = T
TEXT = TEXTO
TEXTJOIN = UNIRTEXTO
TRIM = ARRUMAR
UNICHAR = CARACTUNICODE
UNICODE = UNICODE
UPPER = MAIÚSCULA
VALUE = VALOR

##
## Funções da Web (Web Functions)
##
ENCODEURL = CODIFURL
FILTERXML = FILTROXML
WEBSERVICE = SERVIÇOWEB

##
## Funções de compatibilidade (Compatibility Functions)
##
BETADIST = DISTBETA
BETAINV = BETA.ACUM.INV
BINOMDIST = DISTRBINOM
CEILING = TETO
CHIDIST = DIST.QUI
CHIINV = INV.QUI
CHITEST = TESTE.QUI
CONCATENATE = CONCATENAR
CONFIDENCE = INT.CONFIANÇA
COVAR = COVAR
CRITBINOM = CRIT.BINOM
EXPONDIST = DISTEXPON
FDIST = DISTF
FINV = INVF
FLOOR = ARREDMULTB
FORECAST = PREVISÃO
FTEST = TESTEF
GAMMADIST = DISTGAMA
GAMMAINV = INVGAMA
HYPGEOMDIST = DIST.HIPERGEOM
LOGINV = INVLOG
LOGNORMDIST = DIST.LOGNORMAL
MODE = MODO
NEGBINOMDIST = DIST.BIN.NEG
NORMDIST = DISTNORM
NORMINV = INV.NORM
NORMSDIST = DISTNORMP
NORMSINV = INV.NORMP
PERCENTILE = PERCENTIL
PERCENTRANK = ORDEM.PORCENTUAL
POISSON = POISSON
QUARTILE = QUARTIL
RANK = ORDEM
STDEV = DESVPAD
STDEVP = DESVPADP
TDIST = DISTT
TINV = INVT
TTEST = TESTET
VAR = VAR
VARP = VARP
WEIBULL = WEIBULL
ZTEST = TESTEZ
