<?php

/*
 * This file is part of the overtrue/wechat.
 *
 * (c) overtrue <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace EasyWeChat\OpenPlatform\Authorizer\Aggregate;

use Pi<PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;

class AggregateServiceProvider implements ServiceProviderInterface
{
    public function register(Container $app)
    {
    }
}
